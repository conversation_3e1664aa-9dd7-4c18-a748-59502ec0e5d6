# MCP Integration Consolidation Plan (Revised)

## Analysis Summary

After analyzing the MCP integration patterns across `chat_completions.py`, `response.py`, `gemini.py`, and `claude.py`, I've identified significant code duplication and opportunities for consolidation. All four files implement similar MCP functionality with nearly identical patterns.

**Key Revision**: Focus consolidation on **stdio and streamable-http transports only**, as HTTP transport implementations are provider-specific (OpenAI Response API vs Claude native MCP) and should remain separate.

**Critical Gap Identified**: `gemini.py` lacks circuit breaker functionality for MCP connections, while the other three backends have comprehensive circuit breaker implementations. This creates inconsistent failure handling and reliability across backends.

## Identified Redundant Functions

### 1. **MCP Setup and Initialization (stdio + streamable-http only)**
- `_setup_mcp_tools()` - Nearly identical across all 4 files (300+ lines each)
- `_normalize_mcp_servers()` - Exact duplicate in all files
- `_separate_mcp_servers_by_transport_type()` - Similar logic for stdio/streamable-http separation

### 2. **Circuit Breaker Management (stdio + streamable-http only)**
- `_apply_mcp_tools_circuit_breaker_filtering()` - Identical in chat_completions.py, response.py, claude.py
- `_record_mcp_tools_success()` / `_record_mcp_tools_failure()` - Identical patterns
- `_record_mcp_tools_event()` - Nearly identical implementations
- Circuit breaker status checking for mcp_tools servers

### 3. **Error Handling**
- `_get_mcp_error_info()` / `_mcp_error_details()` - Same error mapping logic
- `_log_mcp_error()` - Similar error logging patterns
- `_is_transient_error()` - Identical static method
- Error handling flows for stdio/streamable-http connections

### 4. **Tool Conversion and Management**
- MCP tool to Function object conversion - Identical closure pattern in all files
- Tool filtering logic - Repeated validation and filtering code
- Function registry management - Similar patterns
- **Function class usage** - All files import and use `Function` from `common.py`

### 5. **Resource Management**
- `cleanup_mcp()` - Nearly identical across all files (for stdio/streamable-http clients)
- Async context manager patterns (`__aenter__`, `__aexit__`)

### 6. **Configuration and Validation**
- MCP configuration validation using `MCPConfigValidator`
- Tool filtering parameter extraction
- Server configuration validation for stdio/streamable-http

**Note**: HTTP transport implementations remain provider-specific and are **excluded** from consolidation:
- OpenAI Response API HTTP MCP servers
- Claude native HTTP MCP servers

## Circuit Breaker Gap Analysis

### Current State
- **chat_completions.py**: ✅ Full circuit breaker implementation
- **response.py**: ✅ Full circuit breaker implementation
- **claude.py**: ✅ Full circuit breaker implementation
- **gemini.py**: ❌ **NO circuit breaker implementation**

### Impact of Missing Circuit Breaker in gemini.py
1. **Inconsistent Reliability**: Gemini backend lacks failure protection that other backends have
2. **Resource Waste**: No protection against repeated connection attempts to failing MCP servers
3. **Poor User Experience**: No graceful degradation when MCP servers fail
4. **Monitoring Gap**: No failure tracking or recovery mechanisms

### Circuit Breaker Features Missing in gemini.py
- Circuit breaker initialization and configuration
- Server failure tracking and exponential backoff
- Circuit breaker filtering before connection attempts
- Success/failure event recording
- Circuit breaker status monitoring

## Comprehensive MCP Utility Function Gap Analysis

### Function Availability Matrix

| Function | chat_completions.py | response.py | gemini.py | claude.py |
|----------|-------------------|-------------|-----------|-----------|
| `_trim_message_history()` | ✅ | ✅ | ❌ | ❌ |
| `_mcp_error_details()` / `_get_mcp_error_info()` | ❌ | ✅ | ✅ | ❌ |
| `_handle_mcp_retry_error()` | ❌ | ❌ | ✅ | ❌ |
| `_handle_mcp_error_and_fallback()` | ❌ | ❌ | ✅ | ❌ |
| `_is_transient_error()` | ✅ | ✅ | ❌ | ❌ |
| `_log_mcp_error()` | ✅ | ✅ | ❌ | ❌ |
| `max_mcp_message_history` config | ✅ | ✅ | ❌ | ❌ |

### Critical Gaps Identified

**1. Message History Management**
- **Missing in**: gemini.py, claude.py
- **Impact**: Unbounded memory growth in MCP execution loops
- **Solution**: Add `_trim_message_history()` and `_max_mcp_message_history` configuration

**2. Standardized Error Handling**
- **Missing in**: chat_completions.py, claude.py (no `_mcp_error_details()`)
- **Missing in**: gemini.py, claude.py (no `_is_transient_error()`, `_log_mcp_error()`)
- **Impact**: Inconsistent error categorization and user messaging
- **Solution**: Standardize error handling functions across all backends

**3. Advanced Retry Logic**
- **Missing in**: chat_completions.py, response.py, claude.py (no `_handle_mcp_retry_error()`)
- **Impact**: Inconsistent retry behavior and user feedback
- **Solution**: Add sophisticated retry error handling with user-friendly messaging

**4. Fallback Mechanisms**
- **Missing in**: chat_completions.py, response.py, claude.py (no `_handle_mcp_error_and_fallback()`)
- **Impact**: Poor graceful degradation when MCP fails
- **Solution**: Add standardized fallback logic with appropriate user messaging

## Circuit Breaker Implementation Plan for gemini.py

### Required Changes to Match Other Backends

**1. Add Circuit Breaker Initialization (in `__init__`)**
```python
# Add to gemini.py __init__ method
self._mcp_tools_circuit_breaker = None
self._circuit_breakers_enabled = MCPCircuitBreaker is not None

if self._circuit_breakers_enabled:
    from ..mcp_tools.circuit_breaker import CircuitBreakerConfig

    mcp_tools_config = CircuitBreakerConfig(
        max_failures=3,
        reset_time_seconds=30,
        backoff_multiplier=2,
        max_backoff_multiplier=8
    )
    self._mcp_tools_circuit_breaker = MCPCircuitBreaker(mcp_tools_config)
```

**2. Add Circuit Breaker Filtering Method**
```python
def _apply_mcp_tools_circuit_breaker_filtering(self, servers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Filter MCP tools servers based on circuit breaker state."""
    if not self._circuit_breakers_enabled or not self._mcp_tools_circuit_breaker:
        return servers

    filtered_servers = []
    for server in servers:
        server_name = server.get("name", "unnamed")
        if not self._mcp_tools_circuit_breaker.should_skip_server(server_name):
            filtered_servers.append(server)
        else:
            logger.debug(f"Circuit breaker: Skipping MCP tools server {server_name} (circuit open)")

    return filtered_servers
```

**3. Add Circuit Breaker Event Recording**
```python
async def _record_mcp_tools_success(self, servers: List[Dict[str, Any]]) -> None:
    """Record successful connection for mcp_tools servers in circuit breaker."""
    if self._mcp_tools_circuit_breaker:
        for server in servers:
            server_name = server.get("name", "unknown")
            self._mcp_tools_circuit_breaker.record_success(server_name)

async def _record_mcp_tools_failure(self, servers: List[Dict[str, Any]], error_message: str) -> None:
    """Record connection failure for mcp_tools servers in circuit breaker."""
    if self._mcp_tools_circuit_breaker:
        for server in servers:
            server_name = server.get("name", "unknown")
            self._mcp_tools_circuit_breaker.record_failure(server_name)
```

**4. Update MCP Setup to Use Circuit Breaker**
```python
# In _setup_mcp_tools method, add circuit breaker filtering:
filtered_servers = self._apply_mcp_tools_circuit_breaker_filtering(normalized_servers)

if not filtered_servers:
    logger.warning("All MCP servers filtered out by circuit breaker")
    return

# Use filtered_servers in MultiMCPClient.create_and_connect
# Add success/failure recording around connection attempts
```

**5. Update Stream Method Connection Retry Logic**
```python
# In stream_with_tools method, add circuit breaker filtering before retry attempts:
filtered_servers = self._apply_mcp_tools_circuit_breaker_filtering(self.mcp_servers)

if not filtered_servers:
    logger.warning("All MCP servers are circuit breaker blocked")
    using_sdk_mcp = False
    # Continue with non-MCP path
```

## Missing Utility Functions Implementation Specifications

### 1. `_trim_message_history()` Implementation

**Source**: `massgen/backend/response.py` lines 725-748
**Target backends**: gemini.py, claude.py

```python
def _trim_message_history(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Trim message history to prevent unbounded growth in MCP execution loop.

    Preserves the first system message if present; keeps only the most recent
    messages up to the configured limit.
    """
    try:
        max_items = int(getattr(self, '_max_mcp_message_history', 200))
    except Exception:
        max_items = 200

    if max_items <= 0 or len(messages) <= max_items:
        return messages

    preserved = []
    remaining = messages
    if messages and messages[0].get("role") == "system":
        preserved = [messages[0]]
        remaining = messages[1:]

    # Keep the most recent items within the limit
    allowed = max_items - len(preserved)
    trimmed_tail = remaining[-allowed:] if allowed > 0 else []
    return preserved + trimmed_tail
```

**Configuration Addition**:
```python
# Add to __init__ method in gemini.py and claude.py
self._max_mcp_message_history = kwargs.pop("max_mcp_message_history", 200)
```

### 2. `_mcp_error_details()` Implementation

**Source**: Pattern from response.py `_get_mcp_error_info()` and gemini.py `_mcp_error_details()`
**Target backends**: chat_completions.py, claude.py

```python
def _mcp_error_details(self, error: Exception, context: Optional[str] = None, *, log: bool = False) -> tuple[str, str, str]:
    """Return standardized MCP error info and optionally log.

    Returns a tuple of (log_type, user_message, error_category).
    """
    if isinstance(error, MCPConnectionError):
        details = ("connection error", "MCP connection failed", "connection")
    elif isinstance(error, MCPTimeoutError):
        details = ("timeout error", "MCP session timeout", "timeout")
    elif isinstance(error, MCPServerError):
        details = ("server error", "MCP server error", "server")
    elif isinstance(error, MCPError):
        details = ("MCP error", "MCP error", "general")
    else:
        details = ("unexpected error", "MCP connection failed", "unknown")

    if log:
        log_type, user_message, error_category = details
        logger.warning(f"MCP {log_type}: {error}" + (f" ({context})" if context else ""))

    return details
```

### 3. `_is_transient_error()` Implementation

**Source**: Pattern from response.py and chat_completions.py
**Target backends**: gemini.py, claude.py

```python
def _is_transient_error(self, error: Exception) -> bool:
    """Determine if an error is transient and should be retried."""
    if isinstance(error, (MCPConnectionError, MCPTimeoutError)):
        return True
    elif isinstance(error, MCPServerError):
        # Some server errors are transient (5xx), others are not (4xx)
        error_str = str(error).lower()
        return any(keyword in error_str for keyword in [
            "timeout", "connection", "network", "temporary", "unavailable",
            "503", "502", "504", "500"
        ])
    elif isinstance(error, (ConnectionError, TimeoutError, OSError)):
        return True
    return False
```

### 4. `_log_mcp_error()` Implementation

**Source**: Pattern from response.py and chat_completions.py
**Target backends**: gemini.py, claude.py

```python
def _log_mcp_error(self, error: Exception, context: str) -> None:
    """Log MCP error with appropriate level and context."""
    log_type, user_message, error_category = self._mcp_error_details(error)

    if error_category in ["connection", "timeout"]:
        logger.warning(f"MCP {log_type} during {context}: {error}")
    elif error_category == "server":
        logger.error(f"MCP {log_type} during {context}: {error}")
    else:
        logger.error(f"MCP {log_type} during {context}: {error}")
```

### 5. `_handle_mcp_retry_error()` Implementation

**Source**: Pattern from gemini.py
**Target backends**: chat_completions.py, response.py, claude.py

```python
async def _handle_mcp_retry_error(
    self, error: Exception, retry_count: int, max_retries: int
) -> tuple[bool, AsyncGenerator[StreamChunk, None]]:
    """Handle MCP retry errors with specific messaging and fallback logic.

    Returns:
        tuple: (should_continue_retrying, error_chunks_generator)
    """
    log_type, user_message, _ = self._mcp_error_details(error)

    # Log the retry attempt
    logger.warning(f"MCP {log_type} on attempt {retry_count}: {error}")

    # Check if we've exhausted retries
    if retry_count >= max_retries:
        async def error_chunks():
            yield StreamChunk(
                type="content",
                content=f"\n⚠️  {user_message} after {max_retries} attempts; falling back to workflow tools\n",
            )
        return False, error_chunks()

    # Continue retrying
    async def empty_chunks():
        return
        yield  # Make this a generator
    return True, empty_chunks()
```

### 6. `_handle_mcp_error_and_fallback()` Implementation

**Source**: Pattern from gemini.py
**Target backends**: chat_completions.py, response.py, claude.py

```python
async def _handle_mcp_error_and_fallback(
    self,
    error: Exception,
    config: Dict[str, Any],
    all_tools: List,
    _stream_with_config,
) -> AsyncGenerator[StreamChunk, None]:
    """Handle MCP errors with specific messaging and fallback to non-MCP tools."""
    self._mcp_tool_failures += 1

    log_type, user_message, _ = self._mcp_error_details(error)

    # Log with specific error type
    logger.warning(
        f"MCP tool call #{self._mcp_tool_calls_count} failed - {log_type}: {error}"
    )

    # Yield user-friendly error message
    yield StreamChunk(
        type="content",
        content=f"\n⚠️  {user_message} ({error}); continuing without MCP tools\n",
    )

    # Continue with fallback logic...
```

## Revised Shared Utility Structure (in massgen/mcp_tools/)

### Enhanced Function Class: `massgen/mcp_tools/function.py`

Create a new enhanced Function class in the mcp_tools directory:

```python
"""
Enhanced Function wrapper for MCP tools across all backend APIs.
Replaces massgen/backend/common.py Function class.
"""
from __future__ import annotations
from typing import Dict, Any, Callable

class Function:
    """Enhanced function wrapper for MCP tools across all backend APIs."""

    def __init__(self, name: str, description: str, parameters: Dict[str, Any], entrypoint: Callable[[str], Any]) -> None:
        self.name = name
        self.description = description
        self.parameters = parameters
        self.entrypoint = entrypoint

    async def call(self, input_str: str) -> Any:
        """Call the function with input string."""
        return await self.entrypoint(input_str)

    def to_openai_format(self) -> Dict[str, Any]:
        """Convert function to OpenAI Response API format."""
        return {
            "type": "function",
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters,
        }

    def to_chat_completions_format(self) -> Dict[str, Any]:
        """Convert to Chat Completions API format."""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.parameters,
            }
        }

    def to_claude_format(self) -> Dict[str, Any]:
        """Convert to Claude API format."""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters,
        }
```

### Core Integration Utilities: `massgen/mcp_tools/integration.py`

```python
"""
Shared MCP integration utilities for stdio and streamable-http transports.
Excludes HTTP transport (provider-specific).
"""
from typing import Dict, List, Any, Optional, Tuple
from .function import Function
from .client import MultiMCPClient

class MCPIntegrationManager:
    """Centralized MCP integration management for stdio/streamable-http only"""

    @staticmethod
    def normalize_mcp_servers(servers) -> List[Dict[str, Any]]:
        """Validate and normalize mcp_servers into a list of dicts."""

    @staticmethod
    def separate_stdio_streamable_servers(servers) -> List[Dict[str, Any]]:
        """Extract only stdio and streamable-http servers."""

    @staticmethod
    async def setup_mcp_client(
        servers: List[Dict[str, Any]],
        allowed_tools: Optional[List[str]],
        exclude_tools: Optional[List[str]],
        circuit_breaker
    ) -> MultiMCPClient:
        """Setup MCP client for stdio/streamable-http servers with circuit breaker protection."""

    @staticmethod
    def convert_tools_to_functions(mcp_client: MultiMCPClient) -> Dict[str, Function]:
        """Convert MCP tools to Function objects with standardized closure pattern."""

    @staticmethod
    def get_error_info(error: Exception) -> Tuple[str, str, str]:
        """Get standardized MCP error information."""

    @staticmethod
    def is_transient_error(error: Exception) -> bool:
        """Determine if an error is transient and should be retried."""

    @staticmethod
    def apply_circuit_breaker_filtering(servers: List[Dict], circuit_breaker) -> List[Dict]:
        """Apply circuit breaker filtering to stdio/streamable-http servers."""

    @staticmethod
    async def record_circuit_breaker_event(
        servers: List[Dict],
        event: str,
        circuit_breaker,
        error_msg: Optional[str] = None
    ):
        """Record circuit breaker events for stdio/streamable-http servers."""

    @staticmethod
    async def cleanup_mcp_client(client: MultiMCPClient):
        """Clean up MCP client connections."""
```

### Configuration Utilities: `massgen/mcp_tools/config_helpers.py`

```python
"""
MCP configuration helpers for backend integration.
"""
from typing import Dict, Any, Optional, List, Tuple

class MCPConfigHelper:
    """MCP configuration management utilities"""

    @staticmethod
    def validate_backend_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate backend MCP configuration using existing MCPConfigValidator."""

    @staticmethod
    def extract_tool_filtering_params(config: Dict[str, Any]) -> Tuple[Optional[List], Optional[List]]:
        """Extract allowed_tools and exclude_tools from configuration."""

    @staticmethod
    def build_circuit_breaker_config(transport_type: str):
        """Build circuit breaker configuration for transport type."""
```

### Format Conversion Utilities: `massgen/mcp_tools/converters.py`

```python
"""
MCP format conversion utilities for different APIs.
"""
from typing import Dict, List, Any
from .function import Function

class MCPConverters:
    """Format conversion utilities for different APIs"""

    @staticmethod
    def to_chat_completions_format(functions: Dict[str, Function]) -> List[Dict[str, Any]]:
        """Convert Function objects to Chat Completions format."""
        return [func.to_chat_completions_format() for func in functions.values()]

    @staticmethod
    def to_response_api_format(functions: Dict[str, Function]) -> List[Dict[str, Any]]:
        """Convert Function objects to Response API format."""
        return [func.to_openai_format() for func in functions.values()]

    @staticmethod
    def to_claude_format(functions: Dict[str, Function]) -> List[Dict[str, Any]]:
        """Convert Function objects to Claude format."""
        return [func.to_claude_format() for func in functions.values()]
```

## 4-Phase Implementation Roadmap

### Phase 1: Create Shared Utilities in mcp_tools/ (Week 1)
**Goal**: Extract common MCP functionality into mcp_tools directory

**Tasks**:
1. **Create `massgen/mcp_tools/function.py`** - Enhanced Function class with multiple format support
2. **Create `massgen/mcp_tools/integration.py`** - Core MCP integration utilities (stdio/streamable-http only)
3. **Create `massgen/mcp_tools/config_helpers.py`** - Configuration management utilities
4. **Create `massgen/mcp_tools/converters.py`** - Format conversion utilities
5. **Create `massgen/mcp_tools/error_handling.py`** - Standardized error handling utilities
6. **Create `massgen/mcp_tools/message_utils.py`** - Message history management utilities
7. **Update `massgen/mcp_tools/__init__.py`** - Export new utilities alongside existing ones
8. Implement comprehensive test coverage for new utilities
9. Ensure backward compatibility during transition

**Expected Outcome**:
- New enhanced Function class in mcp_tools (~60 lines)
- 5 new utility modules with ~600 lines total
- All common stdio/streamable-http MCP patterns centralized
- Standardized error handling and message management
- HTTP transport implementations remain provider-specific
- No changes to existing backend files yet

### Phase 2: Add Missing Utility Functions (Week 2)
**Goal**: Add missing MCP utility functions to achieve consistency across backends

**Tasks**:
1. **Add missing functions to chat_completions.py**:
   - `_mcp_error_details()` - Standardized error categorization (from response.py pattern)
   - `_handle_mcp_retry_error()` - Advanced retry logic (from gemini.py pattern)
   - `_handle_mcp_error_and_fallback()` - Graceful fallback logic (from gemini.py pattern)

2. **Add missing functions to response.py**:
   - `_handle_mcp_retry_error()` - Advanced retry logic (from gemini.py pattern)
   - `_handle_mcp_error_and_fallback()` - Graceful fallback logic (from gemini.py pattern)

3. **Add missing functions to gemini.py**:
   - `_trim_message_history()` - Message history management (from response.py)
   - `_max_mcp_message_history` configuration parameter (default: 200)
   - `_is_transient_error()` - Transient error detection (from response.py pattern)
   - `_log_mcp_error()` - Standardized error logging (from response.py pattern)
   - **Circuit breaker functionality** (as detailed in previous sections)

4. **Add missing functions to claude.py**:
   - `_trim_message_history()` - Message history management (from response.py)
   - `_max_mcp_message_history` configuration parameter (default: 200)
   - `_mcp_error_details()` - Standardized error categorization (from response.py pattern)
   - `_handle_mcp_retry_error()` - Advanced retry logic (from gemini.py pattern)
   - `_handle_mcp_error_and_fallback()` - Graceful fallback logic (from gemini.py pattern)
   - `_is_transient_error()` - Transient error detection (from response.py pattern)
   - `_log_mcp_error()` - Standardized error logging (from response.py pattern)

5. **Integration points**:
   - Add `_trim_message_history()` calls in MCP execution loops
   - Use standardized error handling in retry logic
   - Implement consistent fallback mechanisms

**Expected Outcome**:
- All 4 backends have identical MCP utility functions
- Consistent error handling and user messaging across backends
- Proper message history management prevents memory growth
- Advanced retry logic with user-friendly feedback
- Graceful fallback when MCP connections fail

### Phase 3: Consolidate with Shared Utilities (Week 3)
**Goal**: Replace duplicated code with shared utilities from mcp_tools

**Tasks**:
1. **Update imports** - Import from mcp_tools modules in all backends
2. **Replace duplicated setup/error/cleanup code** - Use shared utilities for stdio/streamable-http transport
3. **Implement format conversion** - Use shared converters and new Function class
4. **Replace error handling** - Use shared error utilities from mcp_tools.error_handling
5. **Replace message management** - Use shared utilities from mcp_tools.message_utils
6. **Preserve provider-specific features**:
   - Gemini: SDK MCP sessions (separate from shared utilities)
   - Claude: Native HTTP MCP servers (separate from shared utilities)
7. **Remove old common.py import** - Replace with new mcp_tools Function import
8. Remove ~300 lines of duplicated stdio/streamable-http code from each file

**Expected Outcome**:
- chat_completions.py: Reduce from ~1400 to ~1000 lines (-400 lines)
- response.py: Reduce from ~1360 to ~960 lines (-400 lines)
- gemini.py: Reduce from ~1120 to ~820 lines (-300 lines)
- claude.py: Reduce from ~1365 to ~1065 lines (-300 lines)
- **All backends use identical shared utilities for stdio/streamable-http**
- Provider-specific MCP features (SDK sessions, native HTTP) preserved and unchanged

### Phase 4: Cleanup and Documentation (Week 4)
**Goal**: Finalize integration, remove old code, and improve maintainability

**Tasks**:
1. **Remove `massgen/backend/common.py`** - All backends now use mcp_tools Function class
2. **Update all imports** - Ensure all references point to new mcp_tools modules
3. **Optimize shared utilities** - Based on real-world usage patterns
4. **Add comprehensive documentation** - Document new mcp_tools utilities and migration patterns
5. **Create migration guide** - For future backend implementations and MCP integrations
6. **Implement additional error recovery** - For stdio/streamable-http connections
7. **Add performance monitoring** - For shared utilities
8. **Create comprehensive tests** - Unit and integration tests for all scenarios
9. **Validate HTTP separation** - Ensure provider-specific HTTP implementations work correctly

**Expected Outcome**:
- Fully documented and optimized stdio/streamable-http MCP integration
- Clear separation between shared utilities and provider-specific features
- Comprehensive test coverage for consolidated code
- Performance baseline established
- Clean codebase with no legacy common.py dependencies

## Expected Benefits

### Code Reduction (Revised)
- **Total Lines Removed**: ~1,400 lines of duplicated stdio/streamable-http code
- **chat_completions.py**: 400 lines removed (29% reduction) + 120 lines added for missing utilities
- **response.py**: 400 lines removed (29% reduction) + 60 lines added for missing utilities
- **gemini.py**: 300 lines removed (27% reduction) + 200 lines added (circuit breaker + missing utilities)
- **claude.py**: 300 lines removed (22% reduction) + 180 lines added for missing utilities
- **common.py**: Removed entirely (~30 lines) - replaced by mcp_tools/function.py
- **New mcp_tools utilities**: ~600 lines added across 6 new files

### Consistency Achievement
- **Before**: Inconsistent MCP utility functions across backends (25-75% coverage per function)
- **After**: 100% consistent MCP utility functions across all backends
- **Circuit Breaker**: 100% coverage (up from 75%)
- **Error Handling**: 100% standardized (up from 50%)
- **Message Management**: 100% coverage (up from 50%)
- **Retry Logic**: 100% advanced retry handling (up from 25%)

### Maintainability Improvements
- **Single Source of Truth**: All stdio/streamable-http MCP logic centralized in mcp_tools
- **Consistent Error Handling**: Standardized error patterns for stdio/streamable-http
- **Easier Testing**: Shared utilities can be tested independently
- **Faster Development**: New backends can reuse existing stdio/streamable-http integration
- **Bug Fixes**: Fix once, apply everywhere (for stdio/streamable-http)
- **Provider Separation**: HTTP implementations remain provider-specific as needed

### Architecture Benefits
- **Composition over Inheritance**: Utility functions imported as needed from mcp_tools
- **Modular Design**: Each utility module has single responsibility
- **Transport Separation**: stdio/streamable-http shared, HTTP provider-specific
- **Provider Flexibility**: Backends can customize HTTP behavior while reusing stdio/streamable-http logic
- **Future-Proof**: Easy to add new stdio/streamable-http features
- **Consistent Location**: All MCP utilities in one directory (mcp_tools)

## Detailed Implementation Examples

### Current State: Duplicated Function Creation Pattern
All 4 backend files contain nearly identical MCP tool to Function conversion:

```python
# Repeated in chat_completions.py, response.py, claude.py (60+ lines each)
for tool_name, tool in self._mcp_client.tools.items():
    try:
        # Fix closure bug by using default parameter to capture tool_name
        def create_tool_entrypoint(captured_tool_name: str = tool_name):
            async def tool_entrypoint(input_str: str) -> Any:
                try:
                    arguments = json.loads(input_str)
                except (json.JSONDecodeError, ValueError) as e:
                    logger.error(f"Invalid JSON arguments for MCP tool {captured_tool_name}: {e}")
                    raise MCPValidationError(...)
                return await self._mcp_client.call_tool(captured_tool_name, arguments)
            return tool_entrypoint

        entrypoint = create_tool_entrypoint()
        function = Function(
            name=tool_name,
            description=tool.description,
            parameters=tool.inputSchema,
            entrypoint=entrypoint,
        )
        self.functions[function.name] = function
    except Exception as e:
        logger.error(f"Failed to register tool {tool_name}: {e}")
```

### Current State: Duplicated Format Conversion
Each backend implements its own format conversion:

```python
# chat_completions.py - converts to Chat Completions format
def _convert_mcp_tools_to_chat_completions_format(self) -> List[Dict[str, Any]]:
    converted_tools = []
    for function in self.functions.values():
        openai_format = function.to_openai_format()
        chat_completions_format = {
            "type": "function",
            "function": {
                "name": openai_format.get("name"),
                "description": openai_format.get("description", ""),
                "parameters": openai_format.get("parameters") or {},
            }
        }
        converted_tools.append(chat_completions_format)
    return converted_tools

# claude.py - converts to Claude format
def _convert_mcp_tools_to_claude_format(self) -> List[Dict[str, Any]]:
    tool_dicts = []
    for function in self.functions.values():
        tool_dict = {
            "name": function.name,
            "description": function.description,
            "parameters": function.parameters,
        }
        tool_dicts.append(tool_dict)
    return self.convert_tools_to_claude_format(tool_dicts)
```

### After: Enhanced Function Class + Shared Utilities

**Enhanced common.py Function class:**
```python
# Enhanced Function class with multiple format support
class Function:
    def to_chat_completions_format(self) -> Dict[str, Any]:
        """Convert to Chat Completions API format."""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.parameters,
            }
        }

    def to_claude_format(self) -> Dict[str, Any]:
        """Convert to Claude API format."""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters,
        }
```

**Simplified backend setup with new imports:**
```python
# New imports from mcp_tools
from ..mcp_tools.integration import MCPIntegrationManager
from ..mcp_tools.function import Function
from ..mcp_tools.converters import MCPConverters

# In each backend file - reduced to ~15 lines
async def _setup_mcp_tools(self) -> None:
    if not self._mcp_tools_servers or self._mcp_initialized:
        return

    try:
        self._mcp_client = await MCPIntegrationManager.setup_mcp_client(
            self._mcp_tools_servers,
            self.allowed_tools,
            self.exclude_tools,
            self._mcp_tools_circuit_breaker
        )
        self.functions = MCPIntegrationManager.convert_tools_to_functions(self._mcp_client)
        self._mcp_initialized = True
    except Exception as e:
        logger.warning(f"MCP setup failed: {e}")
```

**Simplified format conversion:**
```python
# chat_completions.py - now just 2 lines
def _convert_mcp_tools_to_chat_completions_format(self) -> List[Dict[str, Any]]:
    return MCPConverters.to_chat_completions_format(self.functions)

# claude.py - now just 2 lines
def _convert_mcp_tools_to_claude_format(self) -> List[Dict[str, Any]]:
    return MCPConverters.to_claude_format(self.functions)
```

**Updated mcp_tools/__init__.py:**
```python
# Export new utilities alongside existing ones
from .client import MCPClient, MultiMCPClient
from .function import Function  # New enhanced Function class
from .integration import MCPIntegrationManager  # New integration utilities
from .converters import MCPConverters  # New format converters
from .config_helpers import MCPConfigHelper  # New config utilities
# ... existing exports
```

### Shared Utility Implementation Example

```python
# massgen/backend/mcp_integration.py
class MCPIntegrationManager:
    @staticmethod
    async def setup_mcp_client(
        servers: List[Dict[str, Any]],
        allowed_tools: Optional[List[str]],
        exclude_tools: Optional[List[str]],
        circuit_breaker: Optional[MCPCircuitBreaker]
    ) -> MultiMCPClient:
        """Centralized MCP client setup with retry logic and validation."""

        # Validation
        validated_config = MCPConfigHelper.validate_backend_config({
            "mcp_servers": servers,
            "allowed_tools": allowed_tools,
            "exclude_tools": exclude_tools
        })

        # Circuit breaker filtering
        filtered_servers = MCPIntegrationManager.apply_circuit_breaker_filtering(
            validated_config["mcp_servers"], circuit_breaker
        )

        # Retry logic with exponential backoff
        for retry in range(3):
            try:
                client = await MultiMCPClient.create_and_connect(
                    filtered_servers,
                    timeout_seconds=30,
                    allowed_tools=validated_config.get("allowed_tools"),
                    exclude_tools=validated_config.get("exclude_tools")
                )

                # Record success
                await MCPIntegrationManager.record_circuit_breaker_event(
                    filtered_servers, "success", circuit_breaker
                )

                return client

            except (MCPConnectionError, MCPTimeoutError) as e:
                if retry < 2:  # Not last attempt
                    await asyncio.sleep(0.5 * (2 ** retry))
                    continue

                # Record failure and re-raise
                await MCPIntegrationManager.record_circuit_breaker_event(
                    filtered_servers, "failure", circuit_breaker, str(e)
                )
                raise
```

## Implementation Notes

### Backward Compatibility
- All existing MCP configurations will continue to work
- No changes to public APIs or configuration formats
- Gradual migration allows for thorough testing

### Risk Mitigation
- Phase-by-phase approach allows for early issue detection
- Comprehensive testing at each phase
- Rollback capability if issues arise
- Existing functionality preserved throughout

### Success Metrics
- 25%+ reduction in stdio/streamable-http MCP code duplication
- No regression in stdio/streamable-http MCP functionality
- HTTP transport implementations preserved and unchanged
- **100% circuit breaker coverage across all backends** (up from 75%)
- **100% consistent MCP utility functions** across all backends
- **Standardized error handling and user messaging** for all MCP operations
- **Proper message history management** prevents memory growth in all backends
- **Advanced retry logic with user feedback** in all backends
- **Graceful fallback mechanisms** when MCP connections fail
- Improved test coverage for shared MCP integration utilities
- Faster development of new stdio/streamable-http MCP features
- All MCP utilities consolidated in mcp_tools directory
- Clean removal of legacy common.py dependencies

## Next Steps

1. **Review and Approve Revised Plan**: Stakeholder review of mcp_tools-based consolidation approach
2. **Create Task Breakdown**: Detailed tickets for each phase with transport type separation
3. **Set Up Testing Environment**: Comprehensive MCP test scenarios for both shared and provider-specific features
4. **Begin Phase 1**: Start with mcp_tools utility creation
5. **Continuous Integration**: Ensure no regressions in stdio/streamable-http OR HTTP implementations during migration

## Key Advantages of Revised Approach

1. **Logical Organization**: All MCP utilities in established mcp_tools directory
2. **Transport Separation**: stdio/streamable-http shared, HTTP provider-specific
3. **Import Consistency**: Import MCP functionality from single module (mcp_tools)
4. **Reduced Risk**: HTTP implementations unchanged, reducing chance of provider-specific regressions
5. **Future Extensibility**: Easy to add new stdio/streamable-http features while preserving HTTP flexibility
6. **Circuit Breaker Consistency**: All backends will have identical circuit breaker protection
7. **Reliability Improvement**: Gemini backend gains missing failure protection and graceful degradation
